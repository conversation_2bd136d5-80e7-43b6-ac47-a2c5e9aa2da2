import { model, prepararDocumentos } from './geminiClient';
import { PROMPT_FLASHCARDS } from '../../config/prompts';

/**
 * Genera flashcards a partir de los documentos
 */
export async function generarFlashcards(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  cantidad: number = 10,
  instrucciones?: string
): Promise<{ pregunta: string; respuesta: string }[]> {
  try {
    // Preparar el contenido de los documentos
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      throw new Error("No se han proporcionado documentos para generar flashcards.");
    }

    // Construir el prompt para la IA usando el prompt personalizado
    // Reemplazar las variables en el prompt
    let prompt = PROMPT_FLASHCARDS
      .replace('{documentos}', contenidoDocumentos)
      .replace('{cantidad}', cantidad.toString());

    // Añadir instrucciones adicionales si se proporcionan
    if (instrucciones) {
      prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
    } else {
      prompt = prompt.replace('{instrucciones}', '');
    }

    // Generar las flashcards
    const result = await model.generateContent(prompt);
    const responseText = result.response.text();
    let flashcards;

    try {
      flashcards = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Error al parsear JSON de la respuesta de Gemini:', parseError);
      console.error('Respuesta recibida de Gemini (primeros 1000 caracteres):', responseText.substring(0, 1000));
      throw new Error(`No se pudo parsear el JSON de la respuesta del AI. Verifique los logs del servidor para la respuesta completa.`);
    }

    // Validar el formato
    if (!Array.isArray(flashcards)) { // Check if it's an array first
        throw new Error("La respuesta del AI no es un array de flashcards.");
    }
    // Allow empty array as a potentially valid response, but log a warning.
    if (flashcards.length === 0) {
        console.warn("La IA generó una lista vacía de flashcards para la petición dada.");
        // Depending on requirements, one might throw an error here or return the empty array.
        // For now, returning empty array as the parsing itself was successful.
    }
    
    // Optional: Further validation for structure of each flashcard object
    // for (const card of flashcards) {
    //   if (typeof card.pregunta !== 'string' || typeof card.respuesta !== 'string') {
    //     // Consider logging the problematic card or the whole array for debugging
    //     throw new Error("Una o más flashcards generadas no tienen el formato esperado (pregunta/respuesta).");
    //   }
    // }

    return flashcards;
  } catch (error) {
    console.error('Error detallado al generar flashcards:', error); // Log the detailed error
    // Provide a more user-friendly error message or re-throw a custom error
    if (error instanceof Error && error.message.startsWith("No se pudo parsear el JSON")) {
        throw error; // Re-throw the specific parsing error
    }
    // Add more specific error handling if needed
    throw new Error('Ocurrió un error inesperado durante la generación de flashcards. Por favor, revise los logs para más detalles.');
  }
}
