# Supabase Configuration
# Obtén estos valores desde tu proyecto en https://supabase.com/dashboard
NEXT_PUBLIC_SUPABASE_URL=https://tu-proyecto.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_clave_anonima_de_supabase

# Google Gemini API Configuration
# Obtén tu clave API desde https://makersuite.google.com/app/apikey
NEXT_PUBLIC_GEMINI_API_KEY=tu_clave_api_de_gemini

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=tu_secreto_para_nextauth_genera_uno_aleatorio

# Development
NODE_ENV=development
