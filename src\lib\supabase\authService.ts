import { supabase } from './supabaseClient';
import { Session, User } from '@supabase/supabase-js';

/**
 * Inicia sesión con email y contraseña
 */
export async function iniciarSesion(email: string, password: string): Promise<{
  user: User | null;
  session: Session | null; // Added session
  error: string | null;
}> {
  try {
    console.log('Iniciando sesión con:', { email });

    // Verificar que el email y la contraseña no estén vacíos
    if (!email || !password) {
      console.error('Email o contraseña vacíos');
      return {
        user: null,
        session: null, // Added session
        error: 'Por favor, ingresa tu email y contraseña'
      };
    }

    // Intentar iniciar sesión
    console.log('Llamando a signInWithPassword...');

    // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email.trim(),
      password: password,
    });

    console.log('Respuesta de signInWithPassword:', {
      data: data ? {
        user: !!data.user,
        session: !!data.session
      } : null,
      error
    });

    if (error) {
      // Manejar específicamente el error de sincronización de tiempo
      if (error.message.includes('issued in the future') ||
          error.message.includes('clock for skew')) {
        console.error('Error de sincronización de tiempo:', error.message);
        return {
          user: null,
          session: null, // Added session
          error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'
        };
      }

      // Manejar error de credenciales inválidas de forma más amigable
      if (error.message.includes('Invalid login credentials')) {
        return {
          user: null,
          session: null, // Added session
          error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'
        };
      }

      console.error('Error al iniciar sesión:', error.message);
      return { user: null, session: null, error: error.message }; // Added session
    }

    // Ensure data.user and data.session exist before returning
    if (data && data.user && data.session) {
      // La sesión se establece y las @supabase/auth-helpers-nextjs se encargan de la sincronización.
      // No es necesario un delay artificial aquí.
      console.log('Sesión obtenida de Supabase:', { userId: data.user.id, sessionId: data.session.access_token.substring(0, 10) + '...' });
      return { user: data.user, session: data.session, error: null };
    } else {
      // This case should ideally not be reached if Supabase call is successful
      // but provides a fallback if data or its properties are unexpectedly null/undefined.
      console.error('Error al iniciar sesión: Respuesta inesperada de Supabase, faltan datos de usuario o sesión.');
      return { user: null, session: null, error: 'Respuesta inesperada del servidor al iniciar sesión.' };
    }

  } catch (e: any) { // Changed 'error' to 'e' to avoid conflict with 'error' from signInWithPassword
    console.error('Error inesperado al iniciar sesión:', e);
    // Check if 'e' is an Error object and has a message property
    const errorMessage = (e instanceof Error && e.message) ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';
    return {
      user: null,
      session: null, // Added session
      error: errorMessage
    };
  }
}

/**
 * Cierra la sesión del usuario actual
 */
export async function cerrarSesion(): Promise<{ error: string | null }> {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('Error al cerrar sesión:', error.message);
      return { error: error.message };
    }

    return { error: null };
  } catch (error) {
    console.error('Error inesperado al cerrar sesión:', error);
    return { error: 'Ha ocurrido un error inesperado al cerrar sesión' };
  }
}

/**
 * Obtiene la sesión actual del usuario
 */
export async function obtenerSesion(): Promise<{
  session: Session | null;
  error: string | null;
}> {
  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      // Si el error es "Auth session missing", es un caso esperado cuando no hay sesión
      if (error.message === 'Auth session missing!') {
        // No mostramos error en la consola para este caso específico
        return { session: null, error: null };
      }

      console.error('Error al obtener sesión:', error.message);
      return { session: null, error: error.message };
    }

    return { session: data.session, error: null };
  } catch (error) {
    console.error('Error inesperado al obtener sesión:', error);
    return {
      session: null,
      error: 'Ha ocurrido un error inesperado al obtener la sesión'
    };
  }
}

/**
 * Obtiene el usuario actual
 */
export async function obtenerUsuarioActual(): Promise<{
  user: User | null;
  error: string | null;
}> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      // Si el error es "Auth session missing", es un caso esperado cuando no hay sesión
      if (error.message === 'Auth session missing!') {
        // No mostramos error en la consola para este caso específico
        return { user: null, error: null };
      }

      console.error('Error al obtener usuario actual:', error.message);
      return { user: null, error: error.message };
    }

    return { user, error: null };
  } catch (error) {
    console.error('Error inesperado al obtener usuario actual:', error);
    return {
      user: null,
      error: 'Ha ocurrido un error inesperado al obtener el usuario actual'
    };
  }
}

/**
 * Verifica si el usuario está autenticado
 */
export async function estaAutenticado(): Promise<boolean> {
  const { session } = await obtenerSesion();
  return session !== null;
}
