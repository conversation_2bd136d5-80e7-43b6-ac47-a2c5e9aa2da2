/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    // Configuraciones experimentales si son necesarias
  },
  // Configuración para manejar archivos estáticos
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
