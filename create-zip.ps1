# Script de PowerShell para crear un archivo ZIP con los archivos necesarios para ejecutar la aplicación
# Excluye directorios y archivos innecesarios

# Nombre del archivo ZIP
$ZIP_NAME = "oposiciones-app.zip"

# Eliminar el archivo ZIP si ya existe
if (Test-Path $ZIP_NAME) {
    Remove-Item $ZIP_NAME -Force
}

# Crear un archivo README con instrucciones
$README_CONTENT = @"
# Instrucciones para ejecutar la aplicación OposiAI

Este archivo contiene instrucciones para instalar y ejecutar la aplicación OposiAI en un nuevo equipo.

## Requisitos previos

- Node.js (versión 18 o superior)
- npm (normalmente se instala con Node.js)
- Git (opcional, para clonar el repositorio)

## Pasos para la instalación

1. **Descomprimir el archivo ZIP**

   Descomprime el archivo `oposiciones-app.zip` en una carpeta de tu elección.

2. **Instalar dependencias**

   Abre una terminal en la carpeta donde has descomprimido el archivo y ejecuta:

   ```bash
   npm install
   ```

   Este proceso puede tardar varios minutos dependiendo de tu conexión a internet.

3. **Configurar variables de entorno**

   Crea un archivo `.env.local` en la raíz del proyecto con las siguientes variables:

   ```
   NEXT_PUBLIC_SUPABASE_URL=tu_url_de_supabase
   NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_clave_anonima_de_supabase
   NEXT_PUBLIC_GEMINI_API_KEY=tu_clave_api_de_gemini
   ```

   Reemplaza los valores con tus propias claves de API.

4. **Ejecutar la aplicación en modo desarrollo**

   ```bash
   npm run dev
   ```

   La aplicación estará disponible en [http://localhost:3000](http://localhost:3000)

5. **Construir la aplicación para producción (opcional)**

   ```bash
   npm run build
   npm start
   ```

   La aplicación estará disponible en [http://localhost:3000](http://localhost:3000)

## Estructura del proyecto

- `src/app`: Páginas y rutas de la aplicación
- `src/components`: Componentes React reutilizables
- `src/lib`: Bibliotecas y servicios (Supabase, Gemini, etc.)
- `src/config`: Archivos de configuración, incluyendo los prompts personalizados

## Personalización de prompts

Los prompts personalizados para cada funcionalidad se encuentran en `src/lib/gemini/prompts.ts`. Puedes modificarlos según tus necesidades.

## Problemas comunes

- **Error de conexión a Supabase**: Verifica que las variables de entorno sean correctas.
- **Error al generar contenido con Gemini**: Asegúrate de que tu clave API de Gemini sea válida y tenga permisos suficientes.
- **Problemas con las dependencias**: Si encuentras errores al instalar dependencias, prueba con `npm install --legacy-peer-deps`.

## Contacto

Si tienes problemas para instalar o ejecutar la aplicación, puedes contactar al desarrollador.
"@

$README_CONTENT | Out-File -FilePath "README-INSTALACION.md" -Encoding utf8

Write-Host "Archivo README-INSTALACION.md creado"

# Crear una lista de exclusiones
$exclusions = @(
    "node_modules",
    ".next",
    ".git",
    "out",
    ".env.local",
    ".env",
    ".DS_Store",
    "*.zip",
    "create-zip.sh",
    "create-zip.ps1"
)

# Crear un archivo temporal con la lista de exclusiones
$exclusionsFile = "exclusions.txt"
$exclusions | Out-File -FilePath $exclusionsFile -Encoding utf8

# Crear el archivo ZIP usando Compress-Archive
Write-Host "Creando archivo ZIP..."
Add-Type -AssemblyName System.IO.Compression.FileSystem
$compressionLevel = [System.IO.Compression.CompressionLevel]::Optimal

# Obtener todos los archivos y directorios, excluyendo los que están en la lista de exclusiones
$filesToZip = Get-ChildItem -Path . -Recurse | Where-Object {
    $item = $_
    $exclude = $false
    foreach ($exclusion in $exclusions) {
        if ($exclusion.EndsWith("*")) {
            $prefix = $exclusion.Substring(0, $exclusion.Length - 1)
            if ($item.FullName.Contains($prefix)) {
                $exclude = $true
                break
            }
        } elseif ($item.FullName.Contains($exclusion)) {
            $exclude = $true
            break
        }
    }
    return -not $exclude
}

# Crear el archivo ZIP
[System.IO.Compression.ZipFile]::CreateFromDirectory((Get-Location).Path, "$((Get-Location).Path)\$ZIP_NAME", $compressionLevel, $false)

Write-Host "Archivo ZIP creado: $ZIP_NAME"

# Eliminar el archivo temporal de exclusiones
Remove-Item $exclusionsFile -Force
