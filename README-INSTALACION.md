﻿# Instrucciones para ejecutar la aplicaciÃ³n OposiAI

Este archivo contiene instrucciones para instalar y ejecutar la aplicaciÃ³n OposiAI en un nuevo equipo.

## Requisitos previos

- Node.js (versiÃ³n 18 o superior)
- npm (normalmente se instala con Node.js)
- Git (opcional, para clonar el repositorio)

## Pasos para la instalaciÃ³n

1. **Descomprimir el archivo ZIP**

   Descomprime el archivo oposiciones-app.zip en una carpeta de tu elecciÃ³n.

2. **Instalar dependencias**

   Abre una terminal en la carpeta donde has descomprimido el archivo y ejecuta:

   `ash
   npm install
   `

   Este proceso puede tardar varios minutos dependiendo de tu conexiÃ³n a internet.

3. **Configurar variables de entorno**

   Crea un archivo .env.local en la raÃ­z del proyecto con las siguientes variables:

   `
   NEXT_PUBLIC_SUPABASE_URL=tu_url_de_supabase
   NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_clave_anonima_de_supabase
   NEXT_PUBLIC_GEMINI_API_KEY=tu_clave_api_de_gemini
   `

   Reemplaza los valores con tus propias claves de API.

4. **Ejecutar la aplicaciÃ³n en modo desarrollo**

   `ash
   npm run dev
   `

   La aplicaciÃ³n estarÃ¡ disponible en [http://localhost:3000](http://localhost:3000)

5. **Construir la aplicaciÃ³n para producciÃ³n (opcional)**

   `ash
   npm run build
   npm start
   `

   La aplicaciÃ³n estarÃ¡ disponible en [http://localhost:3000](http://localhost:3000)

## Estructura del proyecto

- src/app: PÃ¡ginas y rutas de la aplicaciÃ³n
- src/components: Componentes React reutilizables
- src/lib: Bibliotecas y servicios (Supabase, Gemini, etc.)
- src/config: Archivos de configuraciÃ³n, incluyendo los prompts personalizados

## PersonalizaciÃ³n de prompts

Los prompts personalizados para cada funcionalidad se encuentran en src/lib/gemini/prompts.ts. Puedes modificarlos segÃºn tus necesidades.

## Problemas comunes

- **Error de conexiÃ³n a Supabase**: Verifica que las variables de entorno sean correctas.
- **Error al generar contenido con Gemini**: AsegÃºrate de que tu clave API de Gemini sea vÃ¡lida y tenga permisos suficientes.
- **Problemas con las dependencias**: Si encuentras errores al instalar dependencias, prueba con 
pm install --legacy-peer-deps.

## Contacto

Si tienes problemas para instalar o ejecutar la aplicaciÃ³n, puedes contactar al desarrollador.
