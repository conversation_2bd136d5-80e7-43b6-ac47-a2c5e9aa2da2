import React, { useState } from 'react';
import { guardarDocumento } from '../lib/supabase';

interface DocumentUploaderProps {
  onSuccess?: () => void;
}

export default function DocumentUploader({ onSuccess }: DocumentUploaderProps) {
  const [titulo, setTitulo] = useState('');
  const [contenido, setContenido] = useState('');
  const [categoria, setCategoria] = useState('');
  const [numeroTema, setNumeroTema] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [mensaje, setMensaje] = useState({ texto: '', tipo: '' });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!titulo.trim() || !contenido.trim()) {
      setMensaje({ 
        texto: 'El título y el contenido son obligatorios', 
        tipo: 'error' 
      });
      return;
    }
    
    setIsLoading(true);
    setMensaje({ texto: '', tipo: '' });
    
    try {
      const documento = {
        titulo,
        contenido,
        categoria: categoria || undefined,
        numero_tema: numeroTema ? parseInt(numeroTema) : undefined
      };
      
      const id = await guardarDocumento(documento);
      
      if (id) {
        setMensaje({ 
          texto: 'Documento guardado correctamente', 
          tipo: 'exito' 
        });
        // Limpiar el formulario
        setTitulo('');
        setContenido('');
        setCategoria('');
        setNumeroTema('');
        
        // Llamar a onSuccess si está definido
        if (onSuccess) {
          onSuccess();
        }
      } else {
        setMensaje({ 
          texto: 'Error al guardar el documento', 
          tipo: 'error' 
        });
      }
    } catch (error) {
      console.error('Error al guardar documento:', error);
      setMensaje({ 
        texto: 'Ha ocurrido un error al guardar el documento', 
        tipo: 'error' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setContenido(event.target.result as string);
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <h2 className="text-xl font-bold mb-4">Subir nuevo documento</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="titulo" className="block text-gray-700 text-sm font-bold mb-2">
            Título:
          </label>
          <input
            type="text"
            id="titulo"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            value={titulo}
            onChange={(e) => setTitulo(e.target.value)}
            placeholder="Título del documento"
            disabled={isLoading}
            required
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="categoria" className="block text-gray-700 text-sm font-bold mb-2">
              Categoría:
            </label>
            <select
              id="categoria"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={categoria}
              onChange={(e) => setCategoria(e.target.value)}
              disabled={isLoading}
            >
              <option value="">Seleccionar categoría</option>
              <option value="tema">Tema</option>
              <option value="anexo">Anexo</option>
              <option value="resumen">Resumen</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="numeroTema" className="block text-gray-700 text-sm font-bold mb-2">
              Número de tema:
            </label>
            <input
              type="number"
              id="numeroTema"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={numeroTema}
              onChange={(e) => setNumeroTema(e.target.value)}
              placeholder="Opcional"
              min="1"
              disabled={isLoading}
            />
          </div>
        </div>
        
        <div>
          <label htmlFor="contenido" className="block text-gray-700 text-sm font-bold mb-2">
            Contenido:
          </label>
          <textarea
            id="contenido"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows={10}
            value={contenido}
            onChange={(e) => setContenido(e.target.value)}
            placeholder="Contenido del documento"
            disabled={isLoading}
            required
          />
        </div>
        
        <div>
          <label htmlFor="archivo" className="block text-gray-700 text-sm font-bold mb-2">
            O sube un archivo de texto:
          </label>
          <input
            type="file"
            id="archivo"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            onChange={handleFileUpload}
            accept=".txt"
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500 mt-1">
            Solo archivos .txt
          </p>
        </div>
        
        {mensaje.texto && (
          <div className={`p-3 rounded ${mensaje.tipo === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
            {mensaje.texto}
          </div>
        )}
        
        <div>
          <button
            type="submit"
            className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            disabled={isLoading}
          >
            {isLoading ? 'Guardando...' : 'Guardar documento'}
          </button>
        </div>
      </form>
    </div>
  );
}
