import React, { useEffect, useState } from 'react';
import { RevisionHistorial, obtenerHistorialRevisiones } from '../lib/supabase';

interface RevisionHistoryProps {
  flashcardId: string;
  onClose: () => void;
}

export default function RevisionHistory({ flashcardId, onClose }: RevisionHistoryProps) {
  const [historial, setHistorial] = useState<RevisionHistorial[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const cargarHistorial = async () => {
      setIsLoading(true);
      try {
        const data = await obtenerHistorialRevisiones(flashcardId);
        setHistorial(data);
      } catch (error) {
        console.error('Error al cargar historial:', error);
        setError('No se pudo cargar el historial de revisiones');
      } finally {
        setIsLoading(false);
      }
    };

    cargarHistorial();
  }, [flashcardId]);

  // Formatear la fecha para mostrarla en la lista
  const formatearFecha = (fechaStr: string): string => {
    const fecha = new Date(fechaStr);
    return fecha.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Obtener el color de la dificultad
  const getColorDificultad = (dificultad: string) => {
    switch (dificultad) {
      case 'dificil':
        return 'bg-red-100 text-red-800';
      case 'normal':
        return 'bg-yellow-100 text-yellow-800';
      case 'facil':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold">Historial de revisiones</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-4 overflow-y-auto flex-grow">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
            </div>
          ) : error ? (
            <div className="text-red-500 text-center py-4">{error}</div>
          ) : historial.length === 0 ? (
            <div className="text-gray-500 text-center py-4">No hay historial de revisiones para esta tarjeta</div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-5 gap-2 font-semibold text-sm border-b pb-2">
                <div className="col-span-2">Fecha</div>
                <div>Dificultad</div>
                <div>Intervalo</div>
                <div>Factor</div>
              </div>
              {historial.map((revision) => (
                <div key={revision.id} className="grid grid-cols-5 gap-2 text-sm border-b pb-2 hover:bg-gray-50">
                  <div className="col-span-2">{formatearFecha(revision.fecha)}</div>
                  <div>
                    <span className={`px-2 py-1 rounded-full text-xs ${getColorDificultad(revision.dificultad)}`}>
                      {revision.dificultad.charAt(0).toUpperCase() + revision.dificultad.slice(1)}
                    </span>
                  </div>
                  <div>{revision.intervalo} {revision.intervalo === 1 ? 'día' : 'días'}</div>
                  <div>{revision.factor_facilidad.toFixed(2)}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="p-4 border-t">
          <div className="text-sm text-gray-600">
            <p className="mb-2">Leyenda:</p>
            <div className="flex space-x-4">
              <div className="flex items-center">
                <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
                <span>Difícil</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1"></span>
                <span>Normal</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-3 h-3 rounded-full bg-green-500 mr-1"></span>
                <span>Fácil</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
