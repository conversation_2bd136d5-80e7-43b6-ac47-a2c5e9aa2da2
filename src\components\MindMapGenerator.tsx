import React, { useState } from 'react';
import { Documento } from '../lib/supabase';
import { generarMapaMental } from '../lib/gemini';

interface MindMapGeneratorProps {
  documentosSeleccionados: Documento[];
}

export default function MindMapGenerator({ documentosSeleccionados }: MindMapGeneratorProps) {
  const [peticion, setPeticion] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [mapaGenerado, setMapaGenerado] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!peticion.trim()) {
      setError('Por favor, escribe una petición para el mapa mental');
      return;
    }

    if (documentosSeleccionados.length === 0) {
      setError('Debes seleccionar al menos un documento para generar el mapa mental');
      return;
    }

    setIsLoading(true);
    setError('');
    setMapaGenerado(null);

    try {
      // Extraer el contenido de los documentos seleccionados
      const contextos = documentosSeleccionados.map(doc => doc.contenido);

      // Obtener el mapa mental de la IA
      const codigoMapa = await generarMapaMental(peticion, contextos);
      setMapaGenerado(codigoMapa);
    } catch (error) {
      console.error('Error al generar mapa mental:', error);
      setError('Ha ocurrido un error al generar el mapa mental. Por favor, inténtalo de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = () => {
    if (!mapaGenerado) return;

    // Crear un blob con el contenido TypeScript
    const blob = new Blob([mapaGenerado], { type: 'text/typescript' });

    // Crear un enlace para descargar el archivo
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'mapa-mental.ts';
    document.body.appendChild(a);
    a.click();

    // Limpiar
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 0);
  };

  return (
    <div className="mt-8 border-t pt-8">
      <h2 className="text-xl font-bold mb-4">Generador de Mapas Mentales</h2>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="peticion" className="block text-gray-700 text-sm font-bold mb-2">
            Describe el mapa mental que deseas generar:
          </label>
          <textarea
            id="peticion"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows={3}
            value={peticion}
            onChange={(e) => setPeticion(e.target.value)}
            placeholder="Ej: Genera un mapa mental sobre los conceptos principales del tema 1"
            disabled={isLoading}
          />
          <p className="text-sm text-gray-500 mt-1">
            La IA generará código TypeScript para un módulo de mapa mental interactivo con D3.js basado en los documentos seleccionados y tu petición.
          </p>
        </div>

        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}

        <div>
          <button
            type="submit"
            className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            disabled={isLoading || documentosSeleccionados.length === 0}
          >
            {isLoading ? 'Generando...' : 'Generar Mapa Mental'}
          </button>
        </div>
      </form>

      {isLoading && (
        <div className="mt-4 text-center">
          <p className="text-gray-600">Generando mapa mental, por favor espera...</p>
          <div className="mt-2 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
          </div>
        </div>
      )}

      {mapaGenerado && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Código TypeScript Generado:</h3>
          <div className="bg-gray-900 p-4 rounded-lg border overflow-auto" style={{ maxHeight: '500px' }}>
            <pre className="text-green-400 text-sm font-mono whitespace-pre-wrap">
              <code>{mapaGenerado}</code>
            </pre>
          </div>
          <div className="flex justify-between items-center mt-2">
            <p className="text-sm text-gray-500">
              Código TypeScript del módulo de mapa mental con D3.js. Descarga el archivo para usarlo en tu proyecto.
            </p>
            <button
              type="button"
              onClick={handleDownload}
              className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              Descargar Módulo TypeScript
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
