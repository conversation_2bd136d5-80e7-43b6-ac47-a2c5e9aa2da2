import React, { useState } from 'react';
import { FlashcardConProgreso, DificultadRespuesta } from '@/lib/supabase';
import { IoArrowBack, IoArrowForward } from 'react-icons/io5';
import { motion } from 'framer-motion';

interface FlashcardStudyModeProps {
  flashcards: FlashcardConProgreso[];
  activeIndex: number;
  mostrarRespuesta: boolean;
  respondiendo: boolean;
  onMostrarRespuesta: () => void;
  onRespuesta: (dificultad: DificultadRespuesta) => void;
  onNavigate: (direction: 'prev' | 'next') => void;
  onVolver: () => void;
}

const FlashcardStudyMode: React.FC<FlashcardStudyModeProps> = ({
  flashcards,
  activeIndex,
  mostrarRespuesta,
  respondiendo,
  onMostrarRespuesta,
  onRespuesta,
  onNavigate,
  onVolver
}) => {
  const currentFlashcard = flashcards[activeIndex];
  const [flipped, setFlipped] = useState(false);

  // Manejar la animación de volteo
  const handleFlip = () => {
    setFlipped(!flipped);
    if (!mostrarRespuesta) {
      onMostrarRespuesta();
    }
  };

  return (
    <div className="flex flex-col items-center">
      <div className="w-full flex justify-between items-center mb-4">
        <button
          onClick={onVolver}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <IoArrowBack className="mr-1" /> Volver a colecciones
        </button>
        <div className="text-sm text-gray-500">
          {activeIndex + 1} de {flashcards.length}
        </div>
      </div>

      <div className="w-full max-w-2xl mx-auto">
        <div className="relative w-full h-96 perspective-1000">
          <motion.div
            className={`absolute w-full h-full transform-style-3d transition-transform duration-500 ${
              flipped ? 'rotate-y-180' : ''
            }`}
            animate={{ rotateY: flipped ? 180 : 0 }}
            transition={{ duration: 0.5 }}
          >
            {/* Frente (Pregunta) */}
            <div
              className="absolute w-full h-full backface-hidden bg-white rounded-xl shadow-lg p-8 flex flex-col"
              onClick={handleFlip}
            >
              <div className="text-sm text-gray-500 mb-2">
                {currentFlashcard.progreso?.estado && (
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${
                      currentFlashcard.progreso.estado === 'nuevo'
                        ? 'bg-blue-100 text-blue-800'
                        : currentFlashcard.progreso.estado === 'aprendiendo'
                        ? 'bg-yellow-100 text-yellow-800'
                        : currentFlashcard.progreso.estado === 'repasando'
                        ? 'bg-orange-100 text-orange-800'
                        : 'bg-green-100 text-green-800'
                    }`}
                  >
                    {currentFlashcard.progreso.estado}
                  </span>
                )}
                {!currentFlashcard.progreso?.estado && (
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                    nuevo
                  </span>
                )}
              </div>
              <div className="flex-grow flex items-center justify-center">
                <h3 className="text-xl font-medium text-center">
                  {currentFlashcard.pregunta}
                </h3>
              </div>
              <div className="text-center mt-4">
                <button
                  onClick={handleFlip}
                  className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                >
                  Mostrar respuesta
                </button>
              </div>
            </div>

            {/* Reverso (Respuesta) */}
            <div
              className="absolute w-full h-full backface-hidden bg-white rounded-xl shadow-lg p-8 flex flex-col rotate-y-180"
              onClick={handleFlip}
            >
              <div className="flex-grow flex items-center justify-center">
                <p className="text-lg text-center">{currentFlashcard.respuesta}</p>
              </div>
              {!respondiendo && (
                <div className="flex justify-center space-x-2 mt-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRespuesta('dificil');
                    }}
                    className="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    Difícil
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRespuesta('normal');
                    }}
                    className="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    Normal
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRespuesta('facil');
                    }}
                    className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    Fácil
                  </button>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </div>

      <div className="w-full flex justify-between mt-6">
        <button
          onClick={() => onNavigate('prev')}
          disabled={activeIndex === 0}
          className={`flex items-center ${
            activeIndex === 0
              ? 'text-gray-300 cursor-not-allowed'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <IoArrowBack className="mr-1" /> Anterior
        </button>
        <button
          onClick={() => onNavigate('next')}
          disabled={activeIndex === flashcards.length - 1}
          className={`flex items-center ${
            activeIndex === flashcards.length - 1
              ? 'text-gray-300 cursor-not-allowed'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Siguiente <IoArrowForward className="ml-1" />
        </button>
      </div>
    </div>
  );
};

export default FlashcardStudyMode;
