import { model, prepararDocumentos } from './geminiClient';
import { PROMPT_MAPAS_MENTALES } from '../../config/prompts';

/**
 * Genera un mapa mental a partir de los documentos
 */
export async function generarMapaMental(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  instrucciones?: string
): Promise<any> {
  try {
    // Preparar el contenido de los documentos
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      throw new Error("No se han proporcionado documentos para generar el mapa mental.");
    }

    // Construir el prompt para la IA usando el prompt personalizado
    // Reemplazar las variables en el prompt
    let prompt = PROMPT_MAPAS_MENTALES
      .replace('{documentos}', contenidoDocumentos);

    // Añadir instrucciones adicionales si se proporcionan
    if (instrucciones) {
      prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
    } else {
      prompt = prompt.replace('{instrucciones}', '');
    }

    // Generar el mapa mental
    const result = await model.generateContent(prompt);
    const response = result.response.text();

    // Extraer el JSON de la respuesta
    const jsonMatch = response.match(/\{\s*"id[\s\S]*\}\s*\}/);

    if (!jsonMatch) {
      throw new Error("No se pudo extraer el formato JSON de la respuesta.");
    }

    const mapaJson = jsonMatch[0];
    const mapa = JSON.parse(mapaJson);

    // Validar el formato
    if (!mapa.id || !mapa.name || !mapa.children) {
      throw new Error("El formato del mapa mental generado no es válido.");
    }

    return mapa;
  } catch (error) {
    console.error('Error al generar mapa mental:', error);
    throw error;
  }
}
