# Script alternativo de PowerShell para crear un archivo ZIP
# Usa un enfoque diferente para evitar problemas de acceso a archivos

# Nombre del archivo ZIP
$ZIP_NAME = "oposiai-app.zip"

# Eliminar el archivo ZIP si ya existe
if (Test-Path $ZIP_NAME) {
    Remove-Item $ZIP_NAME -Force
}

# Crear un directorio temporal para los archivos a comprimir
$tempDir = "temp_for_zip"
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

# Directorios y archivos a excluir
$excludeDirs = @(
    "node_modules",
    ".next",
    ".git",
    "out",
    $tempDir
)

$excludeFiles = @(
    ".env",
    ".env.local",
    "*.zip",
    "create-zip.sh",
    "create-zip.ps1",
    "create-zip-alt.ps1"
)

# Copiar archivos y directorios al directorio temporal, excluyendo los no deseados
Write-Host "Copiando archivos necesarios..."
Get-ChildItem -Path . -Recurse | ForEach-Object {
    $relativePath = $_.FullName.Substring((Get-Location).Path.Length + 1)
    
    # Verificar si el archivo o directorio debe ser excluido
    $exclude = $false
    
    # Verificar si es un directorio excluido o está dentro de uno
    foreach ($dir in $excludeDirs) {
        if ($relativePath -eq $dir -or $relativePath.StartsWith("$dir\")) {
            $exclude = $true
            break
        }
    }
    
    # Verificar si es un archivo excluido
    if (-not $exclude -and -not $_.PSIsContainer) {
        foreach ($file in $excludeFiles) {
            if ($file.Contains("*")) {
                $pattern = $file -replace "\*", ".*"
                if ($_.Name -match $pattern) {
                    $exclude = $true
                    break
                }
            } elseif ($_.Name -eq $file) {
                $exclude = $true
                break
            }
        }
    }
    
    # Si no está excluido, copiarlo al directorio temporal
    if (-not $exclude) {
        $targetPath = Join-Path $tempDir $relativePath
        
        # Crear el directorio de destino si no existe
        if ($_.PSIsContainer) {
            if (-not (Test-Path $targetPath)) {
                New-Item -ItemType Directory -Path $targetPath | Out-Null
            }
        } else {
            $targetDir = Split-Path $targetPath -Parent
            if (-not (Test-Path $targetDir)) {
                New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
            }
            Copy-Item $_.FullName -Destination $targetPath -Force
        }
    }
}

# Crear el archivo ZIP
Write-Host "Creando archivo ZIP..."
Compress-Archive -Path "$tempDir\*" -DestinationPath $ZIP_NAME -CompressionLevel Optimal

# Limpiar el directorio temporal
Remove-Item $tempDir -Recurse -Force

Write-Host "Archivo ZIP creado: $ZIP_NAME"
Write-Host "Puedes encontrarlo en: $((Get-Location).Path)\$ZIP_NAME"
