#!/bin/bash

# Crear un archivo ZIP con los archivos necesarios para ejecutar la aplicación
# Excluye directorios y archivos innecesarios

# Nombre del archivo ZIP
ZIP_NAME="oposiciones-app.zip"

# Crear el archivo ZIP
zip -r "$ZIP_NAME" . \
    -x "node_modules/*" \
    -x ".next/*" \
    -x ".git/*" \
    -x "out/*" \
    -x ".env.local" \
    -x ".env" \
    -x ".DS_Store" \
    -x "*.zip" \
    -x "create-zip.sh"

echo "Archivo ZIP creado: $ZIP_NAME"

# Crear un archivo README con instrucciones
cat > README-INSTALACION.md << 'EOF'
# Instrucciones para ejecutar la aplicación OposiAI

Este archivo contiene instrucciones para instalar y ejecutar la aplicación OposiAI en un nuevo equipo.

## Requisitos previos

- Node.js (versión 18 o superior)
- npm (normalmente se instala con Node.js)
- Git (opcional, para clonar el repositorio)

## Pasos para la instalación

1. **Descomprimir el archivo ZIP**

   Descomprime el archivo `oposiciones-app.zip` en una carpeta de tu elección.

2. **Instalar dependencias**

   Abre una terminal en la carpeta donde has descomprimido el archivo y ejecuta:

   ```bash
   npm install
   ```

   Este proceso puede tardar varios minutos dependiendo de tu conexión a internet.

3. **Configurar variables de entorno**

   Crea un archivo `.env.local` en la raíz del proyecto con las siguientes variables:

   ```
   NEXT_PUBLIC_SUPABASE_URL=tu_url_de_supabase
   NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_clave_anonima_de_supabase
   NEXT_PUBLIC_GEMINI_API_KEY=tu_clave_api_de_gemini
   ```

   Reemplaza los valores con tus propias claves de API.

4. **Ejecutar la aplicación en modo desarrollo**

   ```bash
   npm run dev
   ```

   La aplicación estará disponible en [http://localhost:3000](http://localhost:3000)

5. **Construir la aplicación para producción (opcional)**

   ```bash
   npm run build
   npm start
   ```

   La aplicación estará disponible en [http://localhost:3000](http://localhost:3000)

## Estructura del proyecto

- `src/app`: Páginas y rutas de la aplicación
- `src/components`: Componentes React reutilizables
- `src/lib`: Bibliotecas y servicios (Supabase, Gemini, etc.)
- `src/config`: Archivos de configuración, incluyendo los prompts personalizados

## Personalización de prompts

Los prompts personalizados para cada funcionalidad se encuentran en `src/config/prompts.ts`. Puedes modificarlos según tus necesidades.

## Problemas comunes

- **Error de conexión a Supabase**: Verifica que las variables de entorno sean correctas.
- **Error al generar contenido con Gemini**: Asegúrate de que tu clave API de Gemini sea válida y tenga permisos suficientes.
- **Problemas con las dependencias**: Si encuentras errores al instalar dependencias, prueba con `npm install --legacy-peer-deps`.

## Contacto

Si tienes problemas para instalar o ejecutar la aplicación, puedes contactar al desarrollador.
EOF

echo "Archivo README-INSTALACION.md creado"
