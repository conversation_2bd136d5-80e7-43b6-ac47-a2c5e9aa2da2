const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// Crear el archivo ZIP
const output = fs.createWriteStream('oposiciones-ia-deployment.zip');
const archive = archiver('zip', {
  zlib: { level: 9 } // Máxima compresión
});

output.on('close', function() {
  console.log('✅ Archivo ZIP creado exitosamente: oposiciones-ia-deployment.zip');
  console.log('📦 Tamaño total: ' + archive.pointer() + ' bytes');
  console.log('\n📋 Instrucciones para usar en otro PC:');
  console.log('1. Extrae el archivo ZIP');
  console.log('2. Abre una terminal en la carpeta extraída');
  console.log('3. Ejecuta: npm install');
  console.log('4. Configura las variables de entorno en .env.local');
  console.log('5. Ejecuta: npm run dev');
  console.log('6. Abre http://localhost:3000 en tu navegador');
});

archive.on('error', function(err) {
  throw err;
});

archive.pipe(output);

// Archivos y carpetas a incluir
const filesToInclude = [
  // Archivos de configuración del proyecto
  'package.json',
  'package-lock.json',
  'next.config.js',
  'tailwind.config.js',
  'tsconfig.json',
  '.env.example',
  
  // Archivos de documentación
  'README.md',
  
  // Carpeta src completa
  'src/',
  
  // Carpeta public si existe
  'public/',
  
  // Archivos de configuración adicionales
  'postcss.config.js',
  '.gitignore'
];

// Archivos y carpetas a excluir
const excludePatterns = [
  'node_modules',
  '.next',
  '.git',
  'dist',
  'build',
  '.env.local',
  '.env',
  '*.log',
  '.DS_Store',
  'Thumbs.db',
  '*.zip'
];

// Función para verificar si un archivo debe ser excluido
function shouldExclude(filePath) {
  return excludePatterns.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(filePath);
    }
    return filePath.includes(pattern);
  });
}

// Función para agregar archivos al ZIP
function addToArchive(itemPath, archivePath = '') {
  const fullPath = path.resolve(itemPath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  Archivo no encontrado: ${itemPath}`);
    return;
  }
  
  const stats = fs.statSync(fullPath);
  const relativePath = archivePath || itemPath;
  
  if (shouldExclude(relativePath)) {
    return;
  }
  
  if (stats.isDirectory()) {
    // Es un directorio
    const items = fs.readdirSync(fullPath);
    items.forEach(item => {
      const itemFullPath = path.join(fullPath, item);
      const itemArchivePath = path.join(relativePath, item).replace(/\\/g, '/');
      addToArchive(itemFullPath, itemArchivePath);
    });
  } else {
    // Es un archivo
    console.log(`📄 Agregando: ${relativePath}`);
    archive.file(fullPath, { name: relativePath });
  }
}

// Agregar archivos al ZIP
console.log('🚀 Creando paquete de despliegue...\n');

filesToInclude.forEach(item => {
  addToArchive(item);
});

// Crear archivo de instrucciones
const instructions = `# OposiAI - Instrucciones de Instalación

## Requisitos Previos
- Node.js 18 o superior
- npm o yarn

## Instalación

1. **Extraer archivos**
   Extrae todos los archivos de este ZIP en una carpeta

2. **Instalar dependencias**
   \`\`\`bash
   npm install
   \`\`\`

3. **Configurar variables de entorno**
   - Copia \`.env.example\` a \`.env.local\`
   - Edita \`.env.local\` con tus credenciales:
     - NEXT_PUBLIC_SUPABASE_URL: URL de tu proyecto Supabase
     - NEXT_PUBLIC_SUPABASE_ANON_KEY: Clave anónima de Supabase
     - NEXT_PUBLIC_GEMINI_API_KEY: Clave API de Google Gemini
     - NEXTAUTH_SECRET: Secreto para NextAuth (genera uno aleatorio)

4. **Ejecutar la aplicación**
   \`\`\`bash
   npm run dev
   \`\`\`

5. **Acceder a la aplicación**
   Abre http://localhost:3000 en tu navegador

## Funcionalidades
- ✅ Autenticación con Supabase
- ✅ Generación de preguntas con IA (Gemini)
- ✅ Mapas mentales interactivos con D3.js
- ✅ Sistema de flashcards
- ✅ Gestión de documentos

## Soporte
Para problemas o preguntas, revisa la documentación en el código fuente.
`;

archive.append(instructions, { name: 'INSTRUCCIONES.md' });

// Finalizar el archivo
archive.finalize();
