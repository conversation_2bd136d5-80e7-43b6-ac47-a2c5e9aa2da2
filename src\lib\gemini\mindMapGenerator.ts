import { model, prepararDocumentos } from './geminiClient';
import { PROMPT_MAPAS_MENTALES } from '../../config/prompts';

/**
 * Genera un mapa mental a partir de los documentos
 * @returns Código TypeScript completo del módulo de mapa mental con D3.js
 */
export async function generarMapaMental(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  instrucciones?: string
): Promise<string> {
  try {
    // Preparar el contenido de los documentos
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      throw new Error("No se han proporcionado documentos para generar el mapa mental.");
    }

    // Construir el prompt para la IA usando el prompt personalizado
    // Reemplazar las variables en el prompt
    let prompt = PROMPT_MAPAS_MENTALES
      .replace('{documentos}', contenidoDocumentos);

    // Añadir instrucciones adicionales si se proporcionan
    if (instrucciones) {
      prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
    } else {
      prompt = prompt.replace('{instrucciones}', '');
    }

    // Generar el mapa mental
    const result = await model.generateContent(prompt);
    const response = result.response.text();

    // El prompt está configurado para devolver código TypeScript
    // Validar que la respuesta contiene código TypeScript válido
    if (!response.includes('export') || !response.includes('function') || !response.includes('d3')) {
      throw new Error("La respuesta no contiene código TypeScript válido para el mapa mental.");
    }

    // Retornar el código TypeScript directamente
    return response;
  } catch (error) {
    console.error('Error al generar mapa mental:', error);
    throw error;
  }
}
